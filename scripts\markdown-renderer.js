/**
 * Markdown Renderer Module
 * Handles markdown parsing and rendering with syntax highlighting
 */

class MarkdownRenderer {
  constructor() {
    this.initializeMarked();
    this.initializeHighlightJs();
  }

  /**
   * Initialize marked.js with custom configuration
   */
  initializeMarked() {
    if (typeof marked === 'undefined') {
      console.error('Marked.js is not loaded');
      return;
    }

    // Configure marked with basic options and highlight.js integration
    marked.setOptions({
      breaks: true,
      gfm: true,
      tables: true,
      sanitize: false,
      smartLists: true,
      smartypants: false,
      highlight: (code, language) => {
        try {
          return this.highlightCode(code, language);
        } catch (error) {
          console.warn('Highlight error in marked callback:', error);
          return this.escapeHtml(code);
        }
      }
    });

    console.log('Marked.js initialized successfully');
  }

  /**
   * Initialize highlight.js
   */
  initializeHighlightJs() {
    if (typeof hljs === 'undefined') {
      console.error('Highlight.js is not loaded');
      return;
    }

    // Configure highlight.js
    hljs.configure({
      tabReplace: '  ',
      useBR: false,
      classPrefix: 'hljs-'
    });

    console.log('Highlight.js initialized successfully');
    console.log('Available languages:', hljs.listLanguages ? hljs.listLanguages() : 'listLanguages not available');
  }

  /**
   * Highlight code with syntax highlighting
   * @param {string} code - The code to highlight
   * @param {string} language - The programming language
   * @returns {string} Highlighted code HTML
   */
  highlightCode(code, language) {
    // Ensure code is a string
    const codeStr = String(code || '');
    if (!codeStr.trim()) return '';

    // Check if highlight.js is available
    if (typeof hljs === 'undefined') {
      console.warn('Highlight.js not available, returning plain text');
      return this.escapeHtml(codeStr);
    }

    try {
      const langStr = String(language || '').toLowerCase();
      if (langStr && hljs.getLanguage(langStr)) {
        return hljs.highlight(codeStr, { language: langStr }).value;
      } else {
        return hljs.highlightAuto(codeStr).value;
      }
    } catch (error) {
      console.warn('Syntax highlighting failed:', error);
      // Fallback to escaped HTML
      return this.escapeHtml(codeStr);
    }
  }

  /**
   * Escape HTML characters
   * @param {string} text - Text to escape
   * @returns {string} Escaped HTML
   */
  escapeHtml(text) {
    if (typeof EditorUtils !== 'undefined' && EditorUtils.escapeHtml) {
      return EditorUtils.escapeHtml(text);
    }

    return String(text).replace(/[&<>"']/g, (match) => {
      const escapeMap = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return escapeMap[match];
    });
  }

  /**
   * Render markdown to HTML
   * @param {string} markdown - The markdown content
   * @returns {string} Rendered HTML
   */
  render(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return '';
    }

    try {
      // Ensure markdown is properly formatted string
      const cleanMarkdown = String(markdown).trim();

      // Check if marked is available
      if (typeof marked === 'undefined') {
        throw new Error('Marked.js library not loaded');
      }

      // Use marked.parse for newer versions or marked for older versions
      if (typeof marked.parse === 'function') {
        return marked.parse(cleanMarkdown);
      } else if (typeof marked === 'function') {
        return marked(cleanMarkdown);
      } else {
        throw new Error('Marked.js not properly initialized');
      }
    } catch (error) {
      console.error('Markdown rendering failed:', error);
      return `<div style="color: red; padding: 10px; border: 1px solid red; border-radius: 4px;">
        <strong>Error rendering markdown:</strong> ${error.message}
      </div>`;
    }
  }

  /**
   * Render markdown with custom processing
   * @param {string} markdown - The markdown content
   * @returns {string} Processed and rendered HTML
   */
  async renderWithProcessing(markdown) {
    if (!markdown || typeof markdown !== 'string') return '';

    try {
      // Pre-process markdown for basic enhancements
      let processedMarkdown = String(markdown);

      // Add support for task lists
      processedMarkdown = processedMarkdown.replace(/^(\s*)- \[([ x])\] (.+)$/gm, (match, indent, checked, text) => {
        const isChecked = String(checked || '').toLowerCase() === 'x';
        return `${indent}- <input type="checkbox" ${isChecked ? 'checked' : ''} disabled> ${text}`;
      });

      // Add support for highlights
      processedMarkdown = processedMarkdown.replace(/==(.*?)==/g, '<mark>$1</mark>');

      // Add support for keyboard shortcuts
      processedMarkdown = processedMarkdown.replace(/\[\[([^\]]+)\]\]/g, '<kbd>$1</kbd>');

      // Render markdown
      let html = this.render(processedMarkdown);

      // Process Mermaid diagrams if available
      if (window.mermaidIntegration && window.mermaidIntegration.isAvailable()) {
        html = await window.mermaidIntegration.processHtml(html);
      }

      return html;
    } catch (error) {
      console.error('Error in renderWithProcessing:', error);
      return this.render(String(markdown)); // Fallback to basic rendering
    }
  }

  /**
   * Pre-process markdown for custom features
   * @param {string} markdown - The markdown content
   * @returns {string} Processed markdown
   */
  preprocessMarkdown(markdown) {
    try {
      // Ensure markdown is a string
      let processedMarkdown = String(markdown || '');

      // Add support for task lists if not already supported
      processedMarkdown = processedMarkdown.replace(/^(\s*)- \[([ x])\] (.+)$/gm, (match, indent, checked, text) => {
        const isChecked = String(checked || '').toLowerCase() === 'x';
        return `${indent}- <input type="checkbox" ${isChecked ? 'checked' : ''} disabled> ${text}`;
      });

      // Add support for keyboard shortcuts
      processedMarkdown = processedMarkdown.replace(/\[\[([^\]]+)\]\]/g, '<kbd>$1</kbd>');

      // Add support for highlights
      processedMarkdown = processedMarkdown.replace(/==(.*?)==/g, '<mark>$1</mark>');

      return processedMarkdown;
    } catch (error) {
      console.warn('Error in preprocessMarkdown:', error);
      return String(markdown || '');
    }
  }

  /**
   * Post-process HTML for additional features
   * @param {string} html - The rendered HTML
   * @returns {string} Processed HTML
   */
  postprocessHtml(html) {
    // Add copy functionality to code blocks
    html = html.replace(/<button class="copy-code-btn"[^>]*>/g, (match) => {
      return match.replace('onclick="this.copyCode(this)"', 'onclick="MarkdownRenderer.copyCode(this)"');
    });

    return html;
  }

  /**
   * Copy code to clipboard
   * @param {HTMLElement} button - The copy button element
   */
  static copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').textContent;
    
    navigator.clipboard.writeText(code).then(() => {
      const originalIcon = button.innerHTML;
      button.innerHTML = '<i class="mdi mdi-check"></i>';
      button.title = 'Copied!';
      
      setTimeout(() => {
        button.innerHTML = originalIcon;
        button.title = 'Copy code';
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  /**
   * Get table of contents from markdown
   * @param {string} markdown - The markdown content
   * @returns {Array} Array of heading objects
   */
  getTableOfContents(markdown) {
    const headings = [];
    const lines = markdown.split('\n');
    
    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = match[1].length;
        const text = String(match[2] || '').trim();
        const id = text.toLowerCase().replace(/[^\w]+/g, '-');

        headings.push({
          level,
          text,
          id,
          line: index + 1
        });
      }
    });
    
    return headings;
  }

  /**
   * Extract metadata from markdown (front matter)
   * @param {string} markdown - The markdown content
   * @returns {Object} Metadata object and content
   */
  extractMetadata(markdown) {
    const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
    const match = markdown.match(frontMatterRegex);
    
    if (!match) {
      return { metadata: {}, content: markdown };
    }
    
    const yamlContent = match[1];
    const content = match[2];
    const metadata = {};
    
    // Simple YAML parser for basic key-value pairs
    yamlContent.split('\n').forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        metadata[key] = value.replace(/^["']|["']$/g, ''); // Remove quotes
      }
    });
    
    return { metadata, content };
  }
}

// Create global instance
window.markdownRenderer = new MarkdownRenderer();

// Export for module use
window.MarkdownRenderer = MarkdownRenderer;
