# Mermaid Diagram Test

This file contains various Mermaid diagrams to test the integration.

## Flowchart Example

```mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    C --> E[End]
    D --> F[Fix Issues]
    F --> B
```

## Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Editor
    participant Mermaid
    
    User->>Editor: Types diagram code
    Editor->>Mermaid: Process diagram
    Mermaid-->>Editor: Return SVG
    Editor-->>User: Display diagram
```

## Class Diagram

```mermaid
classDiagram
    class MarkdownEditor {
        +String content
        +renderPreview()
        +insertText()
    }
    
    class MermaidIntegration {
        +processHtml()
        +renderDiagrams()
        +getTemplates()
    }
    
    MarkdownEditor --> MermaidIntegration : uses
```

## State Diagram

```mermaid
stateDiagram-v2
    [*] --> Editing
    Editing --> Preview : Switch View
    Preview --> Editing : Switch View
    Editing --> Saving : Ctrl+S
    Saving --> Editing : Complete
    Preview --> [*] : Close
    Editing --> [*] : Close
```

## Pie Chart

```mermaid
pie title Programming Languages Used
    "JavaScript" : 45
    "CSS" : 25
    "HTML" : 20
    "Other" : 10
```

## Invalid Diagram (for error testing)

```mermaid
invalid syntax here
this should show an error
```

## Regular Code Block (should not be processed)

```javascript
// This is regular JavaScript code
function hello() {
    console.log("Hello, World!");
}
```

That's it! The Mermaid integration should render all the valid diagrams above.
