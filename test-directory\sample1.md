# Sample Markdown File 1

This is a test file to verify the directory functionality works correctly.

## Features

- **Bold text** and *italic text*
- `Inline code` and code blocks
- Lists and tables

### Code Example

```javascript
function testDirectoryFeature() {
    console.log("Directory functionality is working!");
    return true;
}
```

### Task List

- [x] Create test files
- [x] Test directory opening
- [ ] Test directory saving
- [ ] Verify recent directories

> This is a blockquote to test markdown rendering.

| Feature | Status |
|---------|--------|
| Directory Open | ✅ |
| Directory Save | ✅ |
| Recent Dirs | ✅ |
