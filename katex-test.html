<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KaTeX Test</title>
  <link rel="stylesheet" href="lib/katex/katex.min.css">
</head>
<body>
  <h1>KaTeX Test</h1>
  
  <p>Inline math: $E = mc^2$</p>
  
  <p>Display math:</p>
  $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
  
  <p>More examples:</p>
  <ul>
    <li>Quadratic formula: $x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$</li>
    <li>E<PERSON><PERSON>'s identity: $e^{i\pi} + 1 = 0$</li>
  </ul>

  <script src="lib/katex/katex.min.js"></script>
  <script src="lib/katex/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      console.log('KaTeX available:', typeof katex !== 'undefined');
      console.log('renderMathInElement available:', typeof renderMathInElement !== 'undefined');
      
      if (typeof renderMathInElement !== 'undefined') {
        renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false
        });
        console.log('Math rendering completed');
      } else {
        console.error('renderMathInElement not available');
      }
    });
  </script>
</body>
</html>
